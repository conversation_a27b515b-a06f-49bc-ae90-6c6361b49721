import joblib
from typing import Optional

class TFModelWrapper:
    """Lightweight wrapper that stores paths and lazily loads the TF model and
    preprocessing artifacts on first predict.
    This lives in a proper importable module to make saved metadata portable.
    """
    def __init__(self, model_path: str, scaler_path: str, labelenc_path: str):
        self.model_path = model_path
        self.scaler_path = scaler_path
        self.labelenc_path = labelenc_path
        self._model = None
        self._scaler = None
        self._le = None

    def _load_if_needed(self):
        if self._model is None:
            from tensorflow import keras
            self._model = keras.models.load_model(self.model_path)
            self._scaler = joblib.load(self.scaler_path)
            self._le = joblib.load(self.labelenc_path)

    def predict(self, X):
        self._load_if_needed()
        Xs = self._scaler.transform(X)
        probs = self._model.predict(Xs)
        idx = probs.argmax(axis=1)
        return self._le.inverse_transform(idx)
