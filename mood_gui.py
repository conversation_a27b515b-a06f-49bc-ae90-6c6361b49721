import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, <PERSON>W<PERSON>t, QVBoxLayout, QPushButton, QLabel, QFileDialog, QTextEdit, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# Keep heavy imports lazy to avoid long import time when opening the GUI
from music_mood_classifier import LIBROSA_OK, MOOD_LABELS


class MusicMoodGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Music Mood Classifier")
        self.setGeometry(200, 200, 640, 420)

        # Overall font
        self.base_font = QFont("Segoe UI", 10)
        self.setFont(self.base_font)

        # Layout
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(18, 18, 18, 18)
        self.layout.setSpacing(12)
        self.setLayout(self.layout)

        # Header
        self.header = QLabel("Music Mood Classifier")
        self.header.setAlignment(Qt.AlignCenter)
        self.header.setStyleSheet("font-size:20px; font-weight:600; color: #222;")
        self.layout.addWidget(self.header)

        # instruction row
        instr = QLabel("Select an MP3 file to analyze its mood and get similar-song suggestions.")
        instr.setAlignment(Qt.AlignCenter)
        instr.setStyleSheet("color: #444; font-size:11px;")
        self.layout.addWidget(instr)

        # Button row
        btn_row = QHBoxLayout()
        btn_row.setContentsMargins(0,0,0,0)
        btn_row.setSpacing(8)
        self.upload_btn = QPushButton("Upload MP3")
        self.upload_btn.setFixedHeight(38)
        self.upload_btn.clicked.connect(self.upload_file)
        self.upload_btn.setCursor(Qt.PointingHandCursor)
        btn_row.addStretch(1)
        btn_row.addWidget(self.upload_btn)
        btn_row.addStretch(1)
        self.layout.addLayout(btn_row)

        # Results
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMinimumHeight(220)
        # nicer background + rounded corners for the result pane
        self.result_text.setStyleSheet(
            "QTextEdit { background: #ffffff; border-radius: 8px; padding: 12px; font-size:12px; color:#111; }"
        )
        self.layout.addWidget(self.result_text)

        # Light theme for the window
        self.setStyleSheet('''
            QWidget { background: qlineargradient(x1:0,y1:0,x2:1,y2:1, stop:0 #f7f9fc, stop:1 #eef3fb); }
            QPushButton { background: qlineargradient(x1:0,y1:0,x2:0,y2:1, stop:0 #5b8def, stop:1 #3b6ee6); color: white; border-radius:6px; padding:6px 14px; }
            QPushButton:hover { background: qlineargradient(x1:0,y1:0,x2:0,y2:1, stop:0 #6ea0ff, stop:1 #4b7ff0); }
            QPushButton:pressed { background: #2f57c2; }
        ''')

        self.mp3_path = None
    def upload_file(self):
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getOpenFileName(self, "Select MP3 File", "", "Audio Files (*.mp3);;All Files (*)", options=options)
        if file_path:
            self.mp3_path = file_path
            self.result_text.setText(f"Selected file:\n{self.mp3_path}\n\nPredicting mood...")
            QApplication.processEvents()  # refresh GUI
            self.predict_mood()

    def predict_mood(self):
        if not LIBROSA_OK:
            self.result_text.setHtml("<b style='color:#b00020'>librosa is not installed;</b> audio processing unavailable.")
            return

        # import the predictor here to avoid executing heavy top-level code during module import
        try:
            from music_mood_classifier import predict_mood_from_mp3
        except Exception as e:
            self.result_text.setText(f"Error importing predictor: {e}")
            return

        try:
            result = predict_mood_from_mp3(self.mp3_path)

            # map moods to gentle colors
            mood_colors = {"Happy": "#f6c84c", "Calm": "#81d4fa", "Sad": "#90a4ae", "Energetic": "#ff8a65"}
            mood = result.get('prediction')
            color = mood_colors.get(mood, '#dddddd')

            # Build HTML output for nicer appearance
            html = []
            html.append(f"<div style='font-size:15px; font-weight:600; color:#222;'>Prediction: <span style='background:{color}; padding:6px 10px; border-radius:6px; color:#111'>{mood}</span></div>")
            html.append(f"<div style='margin-top:8px; color:#444; font-size:12px;'>Model: <b>{result.get('backend')}</b></div>")

            probs = result.get('probabilities') or {}
            if probs:
                html.append("<div style='margin-top:10px; font-size:13px; font-weight:600;'>Confidence</div>")
                html.append("<div style='margin-top:6px;'>")
                for k, v in probs.items():
                    label = k
                    try:
                        ik = int(k)
                        label = MOOD_LABELS[ik]
                    except Exception:
                        pass
                    pct = int(round(v * 100)) if isinstance(v, (float, int)) else 0
                    html.append(f"<div style='margin-bottom:6px; color:#222;'>{label}: <span style='color:#666'>{v:.2f}</span></div>")
                    html.append(f"<div style='background:#e6eef9; border-radius:6px; height:8px; margin-bottom:6px;'><div style='width:{pct}%; background:{color}; height:8px; border-radius:6px;'></div></div>")
                html.append("</div>")
            else:
                html.append("<div style='margin-top:10px;color:#666'>No probability information available.</div>")

            recs = result.get('recommendations', [])
            html.append("<div style='margin-top:12px; font-size:13px; font-weight:600;'>Recommended similar songs</div>")
            if recs:
                html.append("<ul style='margin-top:6px;color:#333'>")
                for r in recs:
                    name = r.get('name') or r.get('id')
                    artist = r.get('artist_name', '')
                    dist = r.get('_dist', 0.0)
                    html.append(f"<li>{name} — <i style='color:#666'>{artist}</i> <span style='color:#999'>({dist:.3f})</span></li>")
                html.append("</ul>")
            else:
                html.append("<div style='color:#666; margin-top:6px'>No recommendations available.</div>")

            self.result_text.setHtml('\n'.join(html))
        except Exception as e:
            self.result_text.setHtml(f"<div style='color:#b00020; font-weight:600'>Error predicting mood</div><pre style='color:#333'>{str(e)}</pre>")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    gui = MusicMoodGUI()
    gui.show()
    sys.exit(app.exec_())