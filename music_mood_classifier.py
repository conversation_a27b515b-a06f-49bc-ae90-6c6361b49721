import os
import ast
import warnings
warnings.filterwarnings("ignore")

from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report, confusion_matrix
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.base import BaseEstimator, ClassifierMixin
import joblib
import argparse
import sys
import time

# Audio (spectrogram) stack
try:
    import librosa
    import soundfile as sf
    LIBROSA_OK = True
except Exception as e:
    print("[Info] librosa not available; audio features/CNN will be disabled.", e)
    LIBROSA_OK = False
# TensorFlow (for CNN) and optional GPU-accelerated tabular (cuML/cupy)
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    # prefer visible GPU and enable memory growth to avoid full allocation
    try:
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            for g in gpus:
                tf.config.experimental.set_memory_growth(g, True)
        print("[Info] TensorFlow version:", tf.__version__, "GPUs:", gpus)
    except Exception as _e:
        print("[Info] TF GPU config check failed:", _e)
    TF_OK = True
except Exception as e:
    print("[Info] TensorFlow not available; CNN will be disabled.", e)
    TF_OK = False

# whether TF can access a GPU device (re-check later if TF_OK)
TF_GPU_AVAILABLE = False
if TF_OK:
    try:
        TF_GPU_AVAILABLE = len(tf.config.list_physical_devices('GPU')) > 0
    except Exception:
        TF_GPU_AVAILABLE = False

# Try to import CuPy for GPU array ops. We do NOT import cuML here because
# cuML/RAPIDS typically requires a conda install on Windows; for non-conda
# environments we keep a TensorFlow MLP fallback for GPU-accelerated tabular
# training instead.
try:
    import cupy as cp
    CUPY_OK = True
    CUML_OK = False
    print('[Info] CuPy available — GPU arrays possible. cuML not imported in this environment.')
except Exception:
    cp = None
    CUPY_OK = False
    CUML_OK = False

# ---------------------
# Configuration
# ---------------------
ARTISTS_CSV = "dataset/artists.csv"
TRACKS_CSV = "dataset/tracks.csv"
ARTIFACTS_DIR = "artifacts"
TABULAR_MODEL_PATH = os.path.join(ARTIFACTS_DIR, "best_tabular_model.joblib")
SCALER_PATH = os.path.join(ARTIFACTS_DIR, "tabular_feature_scaler.joblib")
COMPARISON_CSV = os.path.join(ARTIFACTS_DIR, "model_comparison.csv")
CONFUSION_PNG = os.path.join(ARTIFACTS_DIR, "cm_{name}.png")
CNN_MODEL_PATH = os.path.join(ARTIFACTS_DIR, "cnn_mood.keras")

# Optional audio training data
AUDIO_PATTERN = "{track_id}.mp3"
MAX_AUDIO_SECS = None  # use full audio
MEL_BANDS = 128
SPEC_FRAMES = 128

TRACK_NUMERIC_FEATURES = [
    "danceability", "energy", "valence", "tempo", "loudness", "acousticness",
    "instrumentalness", "liveness", "speechiness", "duration_ms"
]

ARTIST_AGG_FEATURES = [
    "artist_avg_energy", "artist_avg_valence", "artist_avg_danceability",
    "artist_ratio_happy", "artist_ratio_calm", "artist_ratio_sad", "artist_ratio_energetic"
]

MOOD_LABELS = ["Happy", "Calm", "Sad", "Energetic"]

# ---------------------
# Helper functions (merged, fully standalone)
# ---------------------

def _safe_parse_list(x):
    if isinstance(x, list):
        return x
    if not isinstance(x, str):
        return []
    try:
        val = ast.literal_eval(x)
        if isinstance(val, list):
            return val
        return []
    except Exception:
        if "," in x:
            return [t.strip() for t in x.split(",") if t.strip()]
        if ";" in x:
            return [t.strip() for t in x.split(";") if t.strip()]
        return []


def load_spotify_csvs(artists_csv: str, tracks_csv: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    artists = pd.read_csv(artists_csv)
    tracks = pd.read_csv(tracks_csv)
    id_artists_col = next((col for col in ["id_artists", "artists_id", "artist_ids"] if col in tracks.columns), None)
    if id_artists_col:
        tracks["primary_artist_id"] = tracks[id_artists_col].apply(_safe_parse_list).apply(lambda lst: lst[0] if lst else np.nan)
    elif "artist_id" in tracks.columns:
        tracks["primary_artist_id"] = tracks["artist_id"]
    else:
        name_col = "artists" if "artists" in tracks.columns else None
        tracks["primary_artist_id"] = tracks[name_col].astype(str)
        artists["id"] = artists.get("id", artists.get("name"))

    merge_cols = [c for c in ["id", "name", "popularity"] if c in artists.columns]
    artists_small = artists[merge_cols].rename(columns={"id": "primary_artist_id", "name": "artist_name", "popularity": "artist_popularity"})
    tracks = tracks.merge(artists_small, on="primary_artist_id", how="left")
    return artists, tracks


def derive_mood_labels(df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, float]]:
    V_T = df["valence"].median()
    E_T = df["energy"].median()
    def mood_func(v, e):
        if v >= V_T and e >= E_T: return "Happy"
        if v >= V_T and e < E_T: return "Calm"
        if v < V_T and e < E_T: return "Sad"
        return "Energetic"
    df = df.copy()
    df["mood"] = [mood_func(v,e) for v,e in zip(df["valence"], df["energy"])]
    return df, {"V_T": float(V_T), "E_T": float(E_T)}


def add_artist_aggregates(tracks: pd.DataFrame) -> pd.DataFrame:
    agg_means = tracks.groupby("primary_artist_id")[["energy","valence","danceability"]].mean().rename(columns={"energy":"artist_avg_energy","valence":"artist_avg_valence","danceability":"artist_avg_danceability"})
    mood_counts = tracks.pivot_table(index="primary_artist_id", columns="mood", values="id", aggfunc="count", fill_value=0)
    for m in MOOD_LABELS:
        if m not in mood_counts.columns:
            mood_counts[m]=0
    mood_counts = mood_counts[MOOD_LABELS]
    mood_ratios = mood_counts.div(mood_counts.sum(axis=1).replace(0,np.nan),axis=0).fillna(0.0)
    mood_ratios.columns = [f"artist_ratio_{m.lower()}" for m in MOOD_LABELS]
    artist_feats = agg_means.join(mood_ratios, how="outer").reset_index()
    tracks = tracks.merge(artist_feats, on="primary_artist_id", how="left")
    return tracks


def build_feature_matrix(tracks: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
    numeric_cols = [c for c in TRACK_NUMERIC_FEATURES if c in tracks.columns]
    artist_cols = [c for c in ARTIST_AGG_FEATURES if c in tracks.columns]
    used_cols = numeric_cols + artist_cols
    X = tracks[used_cols]
    y = tracks["mood"].astype(str)
    return X, y, used_cols


def stratified_split(X, y, test_size=0.15, val_size=0.15, seed=42):
    X_temp, X_test, y_temp, y_test = train_test_split(X, y, test_size=test_size, stratify=y, random_state=seed)
    rel_val = val_size / (1-test_size)
    X_train, X_val, y_train, y_val = train_test_split(X_temp, y_temp, test_size=rel_val, stratify=y_temp, random_state=seed)
    return X_train, X_val, X_test, y_train, y_val, y_test


def make_preprocessor(numeric_cols: List[str]):
    return Pipeline([("imputer", SimpleImputer(strategy="median")), ("scaler", StandardScaler())])


class XGBLabelEncodedClassifier(BaseEstimator, ClassifierMixin):
    def __init__(self, xgb_model, label_encoder):
        self.xgb_model = xgb_model
        self.label_encoder = label_encoder

    def fit(self, X, y):
        y_encoded = self.label_encoder.fit_transform(y)
        self.xgb_model.fit(X, y_encoded)
        return self

    def predict(self, X):
        y_pred_encoded = self.xgb_model.predict(X)
        return self.label_encoder.inverse_transform(y_pred_encoded)

    def predict_proba(self, X):
        return self.xgb_model.predict_proba(X)


def build_tabular_transformer(input_dim, n_classes, n_heads=4, n_layers=2):
    inputs = keras.Input(shape=(input_dim,))
    x = layers.Dense(64)(inputs)
    x = layers.Reshape((1, 64))(x)
    for _ in range(n_layers):
        attn = layers.MultiHeadAttention(num_heads=n_heads, key_dim=64)(x, x)
        x = layers.Add()([x, attn])
        x = layers.LayerNormalization(epsilon=1e-6)(x)
        ffn = layers.Dense(128, activation="relu")(x)
        ffn = layers.Dense(64)(ffn)
        x = layers.Add()([x, ffn])
        x = layers.LayerNormalization(epsilon=1e-6)(x)
    x = layers.GlobalAveragePooling1D()(x)
    x = layers.Dense(64, activation="relu")(x)
    x = layers.Dropout(0.2)(x)
    outputs = layers.Dense(n_classes, activation="softmax")(x)
    model = keras.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model


def build_tabular_conv1d(input_dim, n_classes):
    inputs = keras.Input(shape=(input_dim, 1))
    x = layers.Conv1D(filters=32, kernel_size=3, activation="relu", padding="same")(
        inputs
    )
    x = layers.MaxPool1D(pool_size=2)(x)
    x = layers.Conv1D(filters=64, kernel_size=3, activation="relu", padding="same")(x)
    x = layers.MaxPool1D(pool_size=2)(x)
    x = layers.GlobalAveragePooling1D()(x)
    x = layers.Dense(64, activation="relu")(x)
    x = layers.Dropout(0.2)(x)
    outputs = layers.Dense(n_classes, activation="softmax")(x)
    model = keras.Model(inputs, outputs)
    model.compile(
        optimizer="adam", loss="sparse_categorical_crossentropy", metrics=["accuracy"]
    )
    return model

def build_tabular_models(numeric_cols: List[str]):
    pre = make_preprocessor(numeric_cols)
    # If cuML is available, attempt to return cuML class references (import inside block)
    if CUML_OK:
        try:
            from cuml.linear_model import LogisticRegression as _cuLogreg
            from cuml.ensemble import RandomForestClassifier as _cuRF
            from cuml.svm import SVC as _cuSVC
            return {
                "logreg": ("cuml", _cuLogreg, pre),
                "rf": ("cuml", _cuRF, pre),
                "svm": ("cuml", _cuSVC, pre)
            }
        except Exception:
            # if imports fail here, fall back to CPU pipelines
            pass
    models = {
        "logreg": Pipeline([("pre", pre), ("clf", LogisticRegression(max_iter=200, multi_class="auto", n_jobs=-1))]),
        "rf": Pipeline([("pre", pre), ("clf", RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1))]),
        "svm": Pipeline([("pre", pre), ("clf", SVC(kernel="rbf", probability=False, C=2.0, gamma="scale", random_state=42))])
    }

    # Add XGBoost model if available
    try:
        from xgboost import XGBClassifier

        xgb_params = {
            "n_estimators": 200,
            "max_depth": 6,
            "learning_rate": 0.1,
            "random_state": 42,
            "n_jobs": -1,
        }
        if CUPY_OK:
            xgb_params["device"] = "cuda"
        xgb_model = XGBClassifier(**xgb_params)
        le = LabelEncoder()
        models["xgboost"] = Pipeline([
            ("pre", pre), ("clf", XGBLabelEncodedClassifier(xgb_model, le))
        ])
    except Exception:
        # XGBoost missing — skip
        pass

    # Add metadata entries for TF transformer and conv1d models so training code
    # can pick them up and optionally train/save them.
    scaler = StandardScaler()
    le = LabelEncoder()
    tf_transformer_path = os.path.join(ARTIFACTS_DIR, "tabular_transformer.keras")
    scaler_path_transformer = os.path.join(ARTIFACTS_DIR, "tabular_transformer_scaler.joblib")
    le_path_transformer = os.path.join(ARTIFACTS_DIR, "tabular_transformer_labelenc.joblib")
    models["transformer"] = (
        scaler,
        le,
        tf_transformer_path,
        scaler_path_transformer,
        le_path_transformer,
    )

    tf_conv1d_path = os.path.join(ARTIFACTS_DIR, "tabular_conv1d.keras")
    scaler_path_conv1d = os.path.join(ARTIFACTS_DIR, "tabular_conv1d_scaler.joblib")
    le_path_conv1d = os.path.join(ARTIFACTS_DIR, "tabular_conv1d_labelenc.joblib")
    models["conv1d"] = (
        scaler,
        le,
        tf_conv1d_path,
        scaler_path_conv1d,
        le_path_conv1d,
    )

    return models

def evaluate_model(name, model, X, y):
    y_pred = model.predict(X)
    acc = accuracy_score(y,y_pred)
    precision, recall, f1, _ = precision_recall_fscore_support(y,y_pred, average="weighted", zero_division=0)
    return {"model": name, "accuracy": acc, "precision": precision, "recall": recall, "f1": f1}

def plot_confusion(y_true, y_pred, labels, title, savepath=None):
    cm = confusion_matrix(y_true, y_pred, labels=labels)
    fig = plt.figure(figsize=(6,5))
    ax = fig.add_subplot(111)
    im = ax.imshow(cm, interpolation='nearest')
    ax.set_title(title)
    ax.set_xlabel("Predicted")
    ax.set_ylabel("True")
    ax.set_xticks(range(len(labels)))
    ax.set_xticklabels(labels, rotation=45, ha='right')
    ax.set_yticks(range(len(labels)))
    ax.set_yticklabels(labels)
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            ax.text(j,i,str(cm[i,j]),ha='center',va='center')
    fig.tight_layout()
    if savepath:
        os.makedirs(os.path.dirname(savepath), exist_ok=True)
        fig.savefig(savepath,dpi=120)
    plt.close(fig)

# CNN Spectrogram helpers
def make_spectrogram(path, sr=22050, n_mels=MEL_BANDS, frames=SPEC_FRAMES):
    y, _ = librosa.load(path, sr=sr, duration=MAX_AUDIO_SECS)
    S = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=n_mels)
    S_db = librosa.power_to_db(S, ref=np.max)
    # ensure fixed frame width
    if S_db.shape[1] < frames:
        pad = frames - S_db.shape[1]
        S_db = np.pad(S_db, ((0,0),(0,pad)), mode='constant')
    else:
        S_db = S_db[:, :frames]
    # normalize
    S_db = (S_db - S_db.mean()) / (S_db.std() + 1e-6)
    return S_db.astype('float32')

def build_cnn_model(input_shape=(MEL_BANDS, SPEC_FRAMES, 1), n_classes=len(MOOD_LABELS)):
    inputs = keras.Input(shape=input_shape)
    x = layers.Conv2D(32, (3,3), activation='relu', padding='same')(inputs)
    x = layers.MaxPool2D((2,2))(x)
    x = layers.Conv2D(64, (3,3), activation='relu', padding='same')(x)
    x = layers.MaxPool2D((2,2))(x)
    x = layers.Conv2D(128, (3,3), activation='relu', padding='same')(x)
    x = layers.GlobalAveragePooling2D()(x)
    x = layers.Dense(128, activation='relu')(x)
    outputs = layers.Dense(n_classes, activation='softmax')(x)
    model = keras.Model(inputs, outputs)
    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])
    return model

def train_cnn_from_tracks(tracks_df, audio_dir='dataset', epochs=15, batch_size=16):
    if not (LIBROSA_OK and TF_OK):
        print('[Info] librosa or TensorFlow not available — skipping CNN training')
        return None
    # Expect tracks_df to have 'id' column matching AUDIO_PATTERN
    X_spec = []
    y = []
    label_map = {m:i for i,m in enumerate(MOOD_LABELS)}
    for _, row in tracks_df.iterrows():
        track_id = row.get('id')
        if pd.isna(track_id):
            continue
        path = os.path.join(audio_dir, AUDIO_PATTERN.format(track_id=track_id))
        if not os.path.exists(path):
            continue
        spec = make_spectrogram(path)
        X_spec.append(spec)
        y.append(label_map.get(row['mood'], 0))
    if not X_spec:
        print('[Info] No audio files found for CNN training')
        return None
    X_np = np.stack(X_spec)[..., np.newaxis]
    y_np = np.array(y, dtype='int64')
    # split
    Xtr, Xte, ytr, yte = train_test_split(X_np, y_np, test_size=0.2, stratify=y_np, random_state=42)
    model = build_cnn_model(input_shape=Xtr.shape[1:], n_classes=len(MOOD_LABELS))
    model.fit(Xtr, ytr, validation_data=(Xte, yte), epochs=epochs, batch_size=batch_size)
    model.save(CNN_MODEL_PATH)
    print(f'[Saved] CNN model -> {CNN_MODEL_PATH}')
    return model

def build_tf_tabular_mlp(input_dim, n_classes, hidden=(128,64)):
    model = keras.Sequential()
    model.add(keras.layers.Input(shape=(input_dim,)))
    for h in hidden:
        model.add(keras.layers.Dense(h, activation='relu'))
        model.add(keras.layers.Dropout(0.2))
    model.add(keras.layers.Dense(n_classes, activation='softmax'))
    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])
    return model


# Top-level wrapper for TensorFlow tabular models. Stores file paths so the
# wrapper itself is picklable; the heavy TF model is loaded lazily on predict.
try:
    from models.tf_wrapper import TFModelWrapper
except Exception:
    # fallback local version if the module isn't available
    class TFModelWrapper:
        def __init__(self, model_path: str, scaler_path: str, labelenc_path: str):
            self.model_path = model_path
            self.scaler_path = scaler_path
            self.labelenc_path = labelenc_path
            self._model = None
            self._scaler = None
            self._le = None
        def _load_if_needed(self):
            if self._model is None:
                from tensorflow import keras
                self._model = keras.models.load_model(self.model_path)
                self._scaler = joblib.load(self.scaler_path)
                self._le = joblib.load(self.labelenc_path)
        def predict(self, X):
            self._load_if_needed()
            Xs = self._scaler.transform(pd.DataFrame(X).fillna(0.0))
            probs = self._model.predict(Xs)
            idx = probs.argmax(axis=1)
            return self._le.inverse_transform(idx)
        def predict_proba(self, X):
            """Return class probabilities aligned with the label encoder's classes."""
            self._load_if_needed()
            Xs = self._scaler.transform(pd.DataFrame(X).fillna(0.0))
            probs = self._model.predict(Xs)
            return probs

# ---------------------
# Load, preprocess, train tabular automatically
# ---------------------

def run_training(force: bool = False):
    os.makedirs(ARTIFACTS_DIR, exist_ok=True)
    artists, tracks = load_spotify_csvs(ARTISTS_CSV, TRACKS_CSV)
    tracks, thresholds = derive_mood_labels(tracks)
    print("Label thresholds:", thresholds)
    tracks = add_artist_aggregates(tracks)
    X, y, used_cols = build_feature_matrix(tracks)
    X_train, X_val, X_test, y_train, y_val, y_test = stratified_split(X, y)
    models = build_tabular_models(used_cols)
    results = []
    trained = {}

    # CLI / artifact guard: skip retraining if artifacts exist
    if not force and os.path.exists(TABULAR_MODEL_PATH):
        print(f"[Info] Found existing tabular model at {TABULAR_MODEL_PATH}; loading and skipping retrain.")
        try:
            loaded = joblib.load(TABULAR_MODEL_PATH)
            # support metadata-based TF wrapper saved as dict
            if isinstance(loaded, dict) and loaded.get("type") == "tf_wrapper":
                best_model = TFModelWrapper(loaded["model_path"], loaded["scaler_path"], loaded["labelenc_path"])
            else:
                best_model = loaded
            trained['best'] = best_model
            # safe eval on test set
            try:
                y_pred_test = best_model.predict(X_test)
                print("\n[TEST] Classification Report (loaded model):\n", classification_report(y_test, y_pred_test, digits=4))
                plot_confusion(y_test, y_pred_test, sorted(y.unique()), title=f"Confusion Matrix — loaded_model (Test)", savepath=CONFUSION_PNG.format(name='loaded'))
                # also save a small model_comparison.csv for the loaded model
                try:
                    res = evaluate_model('loaded', best_model, X_test, y_test)
                    pd.DataFrame([res]).to_csv(COMPARISON_CSV, index=False)
                except Exception as _e:
                    print('[Info] Could not write model comparison for loaded model:', _e)
            except Exception as _e:
                print('[Info] Loaded model could not be evaluated on test set:', _e)
            # exit early to avoid retraining
            print('[Info] Exiting without retraining. Use --force to retrain.')
            return
        except Exception as e:
            print('[Info] Failed to load existing tabular model — will retrain:', e)

    for name, spec in models.items():
        print(f"\n[Training] {name}")
        # Handle tuple specs (TF transformer / conv1d metadata)
        if isinstance(spec, tuple):
            if not TF_OK:
                print(f"[Info] TensorFlow not available; skipping TF model {name}")
                continue
            # spec: (scaler_obj, le_obj, model_path, scaler_path, le_path)
            scaler_obj, le_obj, model_path, scaler_path, le_path = spec
            print(f"[Info] Training TF model for: {name}")
            # fit scaler and label encoder
            scaler = scaler_obj
            Xtr = scaler.fit_transform(X_train.fillna(0.0))
            Xval = scaler.transform(X_val.fillna(0.0))
            Xtest = scaler.transform(X_test.fillna(0.0))
            le = le_obj
            ytr_enc = le.fit_transform(y_train.astype(str))
            yval_enc = le.transform(y_val.astype(str))

            # choose model builder
            if name == 'transformer':
                tf_model = build_tabular_transformer(input_dim=Xtr.shape[1], n_classes=len(le.classes_))
            elif name == 'conv1d':
                # conv1d expects shape (samples, features, 1)
                Xtr_c = Xtr.reshape((Xtr.shape[0], Xtr.shape[1], 1))
                Xval_c = Xval.reshape((Xval.shape[0], Xval.shape[1], 1))
                tf_model = build_tabular_conv1d(input_dim=Xtr.shape[1], n_classes=len(le.classes_))
            else:
                # fallback to simple tabular MLP
                tf_model = build_tf_tabular_mlp(input_dim=Xtr.shape[1], n_classes=len(le.classes_))

            # EarlyStopping
            try:
                from tensorflow.keras.callbacks import EarlyStopping
                es = EarlyStopping(monitor="val_loss", patience=2, restore_best_weights=True)
                callbacks = [es]
            except Exception:
                callbacks = None

            if name == 'conv1d':
                tf_model.fit(Xtr_c, ytr_enc, validation_data=(Xval_c, yval_enc), epochs=15, batch_size=32, callbacks=callbacks, verbose=1)
            else:
                tf_model.fit(Xtr, ytr_enc, validation_data=(Xval, yval_enc), epochs=15, batch_size=32, callbacks=callbacks, verbose=1)

            # save model and preprocessors
            tf_model.save(model_path)
            joblib.dump(scaler, scaler_path)
            joblib.dump(le, le_path)
            wrapped = TFModelWrapper(model_path, scaler_path, le_path)
            trained[name] = wrapped
            res_val = evaluate_model(name + "_val", wrapped, X_val, y_val)
            print("VAL:", res_val)
            results.append(res_val)
            continue

        # spec is expected to be a Pipeline for sklearn models
        pipe = spec
        if TF_OK and TF_GPU_AVAILABLE and not isinstance(pipe, tuple):
            # GPU fallback: train a small TF MLP on scaled features instead of sklearn estimator
            print("[Info] Training tabular model with TensorFlow (GPU) as fallback for:", name)
            scaler = StandardScaler()
            Xtr = scaler.fit_transform(X_train.fillna(0.0))
            Xval = scaler.transform(X_val.fillna(0.0))
            Xtest = scaler.transform(X_test.fillna(0.0))
            le = LabelEncoder()
            ytr_enc = le.fit_transform(y_train.astype(str))
            yval_enc = le.transform(y_val.astype(str))
            tf_model = build_tf_tabular_mlp(input_dim=Xtr.shape[1], n_classes=len(le.classes_))
            try:
                from tensorflow.keras.callbacks import EarlyStopping
                es = EarlyStopping(monitor="val_loss", patience=2, restore_best_weights=True)
                callbacks = [es]
            except Exception:
                callbacks = None
            tf_model.fit(Xtr, ytr_enc, validation_data=(Xval, yval_enc), epochs=15, batch_size=32, callbacks=callbacks, verbose=1)
            tf_model_path = os.path.join(ARTIFACTS_DIR, f"tabular_tf_{name}.keras")
            tf_model.save(tf_model_path)
            scaler_path = os.path.join(ARTIFACTS_DIR, f"tabular_tf_{name}_scaler.joblib")
            le_path = os.path.join(ARTIFACTS_DIR, f"tabular_tf_{name}_labelenc.joblib")
            joblib.dump(scaler, scaler_path)
            joblib.dump(le, le_path)
            wrapped = TFModelWrapper(tf_model_path, scaler_path, le_path)
            trained[name] = wrapped
            res_val = evaluate_model(name + "_val", wrapped, X_val, y_val)
            print("VAL:", res_val)
            results.append(res_val)
        else:
            # sklearn CPU path: spec is Pipeline
            t0 = time.perf_counter()
            pipe.fit(X_train, y_train)
            t1 = time.perf_counter()
            print(f"[Timing] {name} fit time: {t1-t0:.2f}s")
            trained[name] = pipe
            res_val = evaluate_model(name + "_val", pipe, X_val, y_val)
            print("VAL:", res_val)
            results.append(res_val)

    # Final evaluation: run each trained model on the test set and save results + confusion plots
    final_results = []
    for mname, mobj in trained.items():
        try:
            res = evaluate_model(mname, mobj, X_test, y_test)
            final_results.append(res)
            # save confusion matrix
            try:
                y_pred = mobj.predict(X_test)
                plot_confusion(y_test, y_pred, sorted(y.unique()), title=f"Confusion Matrix — {mname} (Test)", savepath=CONFUSION_PNG.format(name=mname))
            except Exception as _e:
                print(f"[Info] Could not create confusion matrix for {mname}:", _e)
        except Exception as _e:
            print(f"[Info] Skipping final evaluation for {mname}:", _e)

    if final_results:
        results_df = pd.DataFrame(final_results).sort_values("f1", ascending=False)
        # save best model artifact (metadata for TF wrappers)
        best_name = results_df.iloc[0]["model"]
        # remove any trailing _val suffix if present
        best_name_clean = best_name.replace("_val", "")
        best_model = trained.get(best_name_clean, trained.get(best_name, None))
        if best_model is not None:
            if isinstance(best_model, TFModelWrapper):
                meta = {
                    "type": "tf_wrapper",
                    "model_path": best_model.model_path,
                    "scaler_path": best_model.scaler_path,
                    "labelenc_path": best_model.labelenc_path,
                }
                joblib.dump(meta, TABULAR_MODEL_PATH)
            else:
                joblib.dump(best_model, TABULAR_MODEL_PATH)

        results_df.to_csv(COMPARISON_CSV, index=False)
        print(f"[Saved] Best tabular model → {TABULAR_MODEL_PATH}")
        print(f"[Saved] Model comparison → {COMPARISON_CSV}")
    else:
        print('[Info] No final evaluation results to save.')


def predict_mood_from_mp3(mp3_path: str):
    """Lightweight loader used by the GUI: loads the saved best model (supports
    TF metadata wrapper) and returns a small dict with prediction and backend.
    """
    # load saved artifact
    if not os.path.exists(TABULAR_MODEL_PATH):
        raise FileNotFoundError("No trained model found; run training first or use --force")
    loaded = joblib.load(TABULAR_MODEL_PATH)
    if isinstance(loaded, dict) and loaded.get("type") == "tf_wrapper":
        model = TFModelWrapper(loaded["model_path"], loaded["scaler_path"], loaded["labelenc_path"])
        backend = "tf_tabular"
    else:
        model = loaded
        backend = "sklearn_tabular"

    # Load dataset to compute medians/defaults and to produce recommendations
    try:
        artists_df, tracks_df = load_spotify_csvs(ARTISTS_CSV, TRACKS_CSV)
    except Exception as e:
        raise RuntimeError(f"Could not load dataset for recommendations: {e}")
    tracks_df, _ = derive_mood_labels(tracks_df)
    tracks_df = add_artist_aggregates(tracks_df)
    X_all, y_all, used_cols = build_feature_matrix(tracks_df)

    # Build a feature vector for the audio file: start with medians then overwrite
    # a few columns with audio-derived proxies where reasonable.
    defaults = X_all.median()
    feat_row = defaults.copy()
    try:
        if LIBROSA_OK:
            y, sr = librosa.load(mp3_path, sr=22050, duration=30)
            duration_ms = int(librosa.get_duration(y=y, sr=sr) * 1000)
            tempo = float(librosa.beat.tempo(y=y, sr=sr)[0]) if hasattr(librosa.beat, 'tempo') else defaults.get('tempo', 0.0)
            rms = float(np.mean(librosa.feature.rms(y=y)))
            centroid = float(np.mean(librosa.feature.spectral_centroid(y=y, sr=sr)))
            # approximate loudness from rms (very rough)
            loudness = 20.0 * np.log10(max(rms, 1e-9))
            # map loudness into dataset loudness range to get an energy proxy
            if 'loudness' in feat_row.index:
                lo, hi = X_all['loudness'].min(), X_all['loudness'].max()
                # normalize and clip
                eng = (loudness - lo) / (hi - lo) if hi > lo else 0.0
                eng = float(np.clip(eng, 0.0, 1.0))
                if 'energy' in feat_row.index:
                    feat_row['energy'] = eng
            if 'tempo' in feat_row.index:
                feat_row['tempo'] = tempo
            if 'duration_ms' in feat_row.index:
                feat_row['duration_ms'] = duration_ms
            if 'loudness' in feat_row.index:
                feat_row['loudness'] = loudness
    except Exception:
        # if audio feature extraction fails, keep medians
        pass

    # ensure order and shape
    X_feat = pd.DataFrame([feat_row[used_cols].fillna(0.0).values], columns=used_cols)

    # Model prediction + probabilities (robust across pipeline, xgb wrapper, TF wrapper)
    if not hasattr(model, 'predict'):
        raise RuntimeError('Loaded model has no predict method')

    # helper: get final clf inside a Pipeline
    def _final_clf(m):
        try:
            return m.named_steps.get('clf', m) if hasattr(m, 'named_steps') else m
        except Exception:
            return m

    def _classes_for(m):
        clf = _final_clf(m)
        # sklearn-style classes_
        if hasattr(clf, 'classes_'):
            return [str(x) for x in clf.classes_]
        # our XGBLabelEncodedClassifier stores label_encoder
        if hasattr(clf, 'label_encoder'):
            return [str(x) for x in clf.label_encoder.classes_]
        # TFModelWrapper internal label encoder
        if hasattr(clf, '_le'):
            try:
                # ensure loaded
                if getattr(clf, '_le', None) is None and hasattr(clf, '_load_if_needed'):
                    clf._load_if_needed()
                if getattr(clf, '_le', None) is not None:
                    return [str(x) for x in clf._le.classes_]
            except Exception:
                pass
        # fallback to global MOOD_LABELS
        return [str(x) for x in MOOD_LABELS]

    probs = None
    classes = _classes_for(model)

    # Try predict_proba first
    try:
        if hasattr(model, 'predict_proba'):
            try:
                p = model.predict_proba(X_feat)
            except Exception:
                # some wrappers expect numpy
                p = model.predict_proba(X_feat.values)
            if p is not None and p.shape[0] >= 1:
                # map columns to classes (if mismatch, pad/trim)
                ncols = p.shape[1]
                if len(classes) != ncols:
                    # try to recompute classes from final clf
                    classes = _classes_for(model)
                probs = {}
                for i in range(ncols):
                    lab = classes[i] if i < len(classes) else str(i)
                    probs[lab] = float(p[0, i])
    except Exception:
        probs = None

    # If no predict_proba, try decision_function + softmax
    if probs is None:
        try:
            clf = _final_clf(model)
            if hasattr(clf, 'decision_function'):
                try:
                    df = clf.decision_function(X_feat)
                except Exception:
                    df = clf.decision_function(X_feat.values)
                # ensure 2D
                if df.ndim == 1:
                    # binary -> convert to two-column scores
                    df = np.vstack([-df, df]).T
                # softmax
                exp = np.exp(df - np.max(df, axis=1, keepdims=True))
                p = exp / np.sum(exp, axis=1, keepdims=True)
                ncols = p.shape[1]
                classes = _classes_for(model)
                probs = { (classes[i] if i < len(classes) else str(i)): float(p[0, i]) for i in range(ncols) }
        except Exception:
            probs = None

    # If still no probs and TF underlying model is available, try direct TF predict
    if probs is None:
        try:
            # TFModelWrapper-like
            if hasattr(model, '_model') or (hasattr(model, 'named_steps') and hasattr(_final_clf(model), '_model')):
                tf_wrapper = model if hasattr(model, '_model') else _final_clf(model)
                try:
                    tf_wrapper._load_if_needed()
                except Exception:
                    pass
                Xs = None
                if hasattr(tf_wrapper, '_scaler') and tf_wrapper._scaler is not None:
                    Xs = tf_wrapper._scaler.transform(X_feat.fillna(0.0))
                else:
                    Xs = X_feat.fillna(0.0).values
                p = tf_wrapper._model.predict(Xs)
                classes = _classes_for(tf_wrapper)
                probs = { (classes[i] if i < len(classes) else str(i)): float(p[0, i]) for i in range(p.shape[1]) }
        except Exception:
            probs = None

    # As a last fallback, call predict() and give 1.0 to predicted class
    if probs is None:
        try:
            try:
                preds = model.predict(X_feat)
            except Exception:
                preds = model.predict(X_feat.values)
            pred_label = preds[0] if isinstance(preds, (list, np.ndarray)) else str(preds)
            probs = {str(pred_label): 1.0}
        except Exception as e:
            raise RuntimeError(f'Failed to obtain prediction/probabilities: {e}')
    else:
        # align pred_label with highest-probability class
        try:
            # pick argmax
            max_lab = max(probs.items(), key=lambda kv: kv[1])[0]
            pred_label = str(max_lab)
        except Exception:
            pred_label = None

    # Recommendations: find tracks with same mood and closest in normalized used_cols space
    try:
        # prepare normalized feature matrix
        M = X_all[used_cols].fillna(0.0).astype(float)
        mins = M.min()
        maxs = M.max()
        denom = (maxs - mins).replace(0, 1.0)
        M_norm = (M - mins) / denom
        q = (X_feat[used_cols].astype(float) - mins) / denom
        dists = np.sqrt(((M_norm - q.values[0]) ** 2).sum(axis=1))
        candidates = tracks_df[y_all == pred_label].copy()
        if candidates.empty:
            candidates = tracks_df
        candidates = candidates.assign(_dist=dists.loc[candidates.index])
        recs = candidates.sort_values('_dist').head(5)[['id', 'name', 'artist_name', '_dist']].to_dict(orient='records')
    except Exception:
        recs = []

    return {"prediction": str(pred_label), "probabilities": probs, "backend": backend, "recommendations": recs}


if __name__ == "__main__":
    # CLI entrypoint: call run_training; use --force to retrain even if artifacts exist
    parser = argparse.ArgumentParser()
    parser.add_argument("--force", action="store_true", help="Force retraining even if artifacts exist")
    parsed = parser.parse_args()
    run_training(force=parsed.force)