import glob
import traceback
import os

import tensorflow as tf

base = os.path.dirname(os.path.dirname(__file__))
art = os.path.join(base, 'artifacts')
pattern = os.path.join(art, 'tabular_tf_*.keras')
files = glob.glob(pattern)
if not files:
    print('No tabular_tf_*.keras files found in', art)
    raise SystemExit(1)

for f in files:
    print('\n--- Loading', f)
    try:
        m = tf.keras.models.load_model(f)
        print('Loaded OK: model name:', getattr(m, 'name', None))
        try:
            print('Layers:')
            for L in m.layers[:10]:
                print(' -', L.name, type(L))
        except Exception:
            pass
    except Exception as e:
        print('FAILED to load:', e)
        traceback.print_exc()
